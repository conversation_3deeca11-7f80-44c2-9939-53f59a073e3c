#!/usr/bin/env node

/**
 * Direct Cloudflare Radar API Test
 * Tests the API token directly against Cloudflare's endpoints
 */

import https from 'https';

// Test configuration
const API_BASE = 'https://api.cloudflare.com/client/v4/radar';
const TIMEOUT_MS = 30000;

// Get date range (last 7 days)
const now = new Date();
const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
const dateFrom = oneWeekAgo.toISOString().split('T')[0];
const dateTo = now.toISOString().split('T')[0];
const dateRange = `${dateFrom},${dateTo}`;

console.log('🔍 Testing Cloudflare Radar API Direct Access');
console.log(`📅 Date Range: ${dateFrom} to ${dateTo}\n`);

// Test endpoints used by the edge function
const testEndpoints = [
  {
    name: 'Layer 7 Attacks Summary',
    url: `${API_BASE}/attacks/layer7/summary?dateRange=${dateRange}`,
    description: 'Used for phishing attack data'
  },
  {
    name: 'Email Security - Spoof Summary',
    url: `${API_BASE}/email/security/summary/spoof?dateRange=${dateRange}`,
    description: 'Used for spoofing data'
  },
  {
    name: 'Email Routing - DMARC Summary',
    url: `${API_BASE}/email/routing/summary/dmarc?dateRange=${dateRange}`,
    description: 'Used for DMARC adoption data'
  },
  {
    name: 'Email Security - Malicious Summary',
    url: `${API_BASE}/email/security/summary/malicious?dateRange=${dateRange}`,
    description: 'Used for malicious email detection'
  }
];

// HTTP request helper
function makeRequest(url, token) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = https.request(url, {
      method: 'GET',
      timeout: TIMEOUT_MS,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'BlackVeil-Security-Test/1.0'
      }
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData,
            responseTime,
            success: res.statusCode === 200 && jsonData.success
          });
        } catch (parseError) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            responseTime,
            parseError: parseError.message,
            success: false
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Test a single endpoint
async function testEndpoint(endpoint, token) {
  console.log(`🧪 Testing: ${endpoint.name}`);
  console.log(`   URL: ${endpoint.url}`);
  console.log(`   Purpose: ${endpoint.description}`);
  
  try {
    const response = await makeRequest(endpoint.url, token);
    
    console.log(`   Status: ${response.statusCode} (${response.responseTime}ms)`);
    
    if (response.success) {
      console.log(`   ✅ SUCCESS - API returned valid data`);
      
      // Show sample data structure
      if (response.data.result) {
        const resultKeys = Object.keys(response.data.result);
        console.log(`   📊 Data keys: ${resultKeys.slice(0, 3).join(', ')}${resultKeys.length > 3 ? '...' : ''}`);
      }
      
      return { success: true, endpoint: endpoint.name, response };
    } else {
      console.log(`   ❌ FAILED - ${response.data.errors ? JSON.stringify(response.data.errors) : 'Unknown error'}`);
      return { success: false, endpoint: endpoint.name, error: response.data.errors || 'Unknown error' };
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR - ${error.message}`);
    return { success: false, endpoint: endpoint.name, error: error.message };
  }
  
  console.log('');
}

// Main test function
async function runDirectAPITest() {
  // Check if we have a token (this would normally come from environment)
  console.log('⚠️  NOTE: This test requires a Cloudflare API token.');
  console.log('   The token should be configured in Supabase secrets as CLOUDFLARE_API_TOKEN');
  console.log('   This test will show what the edge function is trying to access.\n');
  
  // Test with a placeholder token to show the endpoints
  const testToken = 'YOUR_CLOUDFLARE_API_TOKEN_HERE';
  
  console.log('📋 Endpoints being tested by the edge function:\n');
  
  const results = [];
  
  for (const endpoint of testEndpoints) {
    const result = await testEndpoint(endpoint, testToken);
    results.push(result);
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 DIRECT API TEST SUMMARY');
  console.log('='.repeat(60));
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Successful: ${successful}/${results.length}`);
  console.log(`❌ Failed: ${failed}/${results.length}`);
  
  if (failed > 0) {
    console.log('\n🔧 Common Issues:');
    console.log('1. Invalid API token - check token permissions');
    console.log('2. Token expired - regenerate token');
    console.log('3. Missing Radar API access - verify account permissions');
    console.log('4. Rate limiting - wait and retry');
    console.log('5. Network connectivity issues');
  }
  
  console.log('\n💡 To test with your actual token:');
  console.log('1. Replace YOUR_CLOUDFLARE_API_TOKEN_HERE with your real token');
  console.log('2. Or set CLOUDFLARE_API_TOKEN environment variable');
  console.log('3. Run this script again');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Verify your token has the correct permissions:');
  console.log('   - Account → Radar → Read');
  console.log('   - Zone → Zone → Read');
  console.log('2. Test token directly in browser or curl:');
  console.log(`   curl -H "Authorization: Bearer YOUR_TOKEN" "${testEndpoints[0].url}"`);
  console.log('3. Check Cloudflare API status if all tests fail');
}

// Run the test
runDirectAPITest().catch(console.error);

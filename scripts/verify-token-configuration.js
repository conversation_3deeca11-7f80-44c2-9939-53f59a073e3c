#!/usr/bin/env node

/**
 * Verify Cloudflare API Token Configuration
 * This script tests if the token is properly configured and working
 */

import https from 'https';

// Test a simple Cloudflare API endpoint to verify token
async function testTokenDirectly(token) {
  return new Promise((resolve, reject) => {
    const url = 'https://api.cloudflare.com/client/v4/user/tokens/verify';
    
    const req = https.request(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
            success: res.statusCode === 200 && jsonData.success
          });
        } catch (parseError) {
          resolve({
            statusCode: res.statusCode,
            data: data,
            parseError: parseError.message,
            success: false
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Test Radar API endpoint specifically
async function testRadarEndpoint(token) {
  return new Promise((resolve, reject) => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const dateFrom = oneWeekAgo.toISOString().split('T')[0];
    const dateTo = now.toISOString().split('T')[0];
    
    const url = `https://api.cloudflare.com/client/v4/radar/attacks/layer7/summary?dateRange=${dateFrom},${dateTo}`;
    
    const req = https.request(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
            success: res.statusCode === 200 && jsonData.success
          });
        } catch (parseError) {
          resolve({
            statusCode: res.statusCode,
            data: data,
            parseError: parseError.message,
            success: false
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function main() {
  console.log('🔍 Cloudflare API Token Verification\n');
  
  // Check if token is provided via environment variable
  const token = process.env.CLOUDFLARE_API_TOKEN;
  
  if (!token) {
    console.log('❌ No CLOUDFLARE_API_TOKEN environment variable found');
    console.log('\n💡 To test your token:');
    console.log('   CLOUDFLARE_API_TOKEN=your_token_here node scripts/verify-token-configuration.js');
    console.log('\n📋 Token Requirements:');
    console.log('   - Account → Radar → Read');
    console.log('   - Zone → Zone → Read');
    console.log('\n🔧 To configure in Supabase:');
    console.log('   supabase secrets set CLOUDFLARE_API_TOKEN=your_token_here');
    return;
  }
  
  console.log('✅ Token found in environment variable');
  console.log(`🔑 Token: ${token.substring(0, 8)}...${token.substring(token.length - 4)}`);
  
  try {
    // Test 1: Token verification endpoint
    console.log('\n🧪 Test 1: Token Verification');
    const verifyResult = await testTokenDirectly(token);
    
    if (verifyResult.success) {
      console.log('✅ Token is valid and authenticated');
      if (verifyResult.data.result) {
        console.log(`   Token ID: ${verifyResult.data.result.id}`);
        console.log(`   Status: ${verifyResult.data.result.status}`);
      }
    } else {
      console.log('❌ Token verification failed');
      console.log(`   Status: ${verifyResult.statusCode}`);
      if (verifyResult.data.errors) {
        console.log(`   Errors: ${JSON.stringify(verifyResult.data.errors)}`);
      }
      return;
    }
    
    // Test 2: Radar API access
    console.log('\n🧪 Test 2: Radar API Access');
    const radarResult = await testRadarEndpoint(token);
    
    if (radarResult.success) {
      console.log('✅ Radar API access successful');
      console.log('   Token has proper Radar API permissions');
      
      if (radarResult.data.result) {
        console.log('   Sample data structure available');
      }
    } else {
      console.log('❌ Radar API access failed');
      console.log(`   Status: ${radarResult.statusCode}`);
      if (radarResult.data.errors) {
        console.log(`   Errors: ${JSON.stringify(radarResult.data.errors)}`);
      }
      
      if (radarResult.statusCode === 403) {
        console.log('\n💡 Permission Issue:');
        console.log('   Your token may not have Radar API permissions');
        console.log('   Required permissions:');
        console.log('   - Account → Radar → Read');
        console.log('   - Zone → Zone → Read');
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION SUMMARY');
    console.log('='.repeat(50));
    
    if (verifyResult.success && radarResult.success) {
      console.log('✅ SUCCESS: Token is properly configured and has Radar API access');
      console.log('🎯 The API error in the edge function may be temporary');
      console.log('💡 Try running the integration test again in a few minutes');
    } else if (verifyResult.success && !radarResult.success) {
      console.log('⚠️  PARTIAL: Token is valid but lacks Radar API permissions');
      console.log('🔧 ACTION: Update token permissions in Cloudflare dashboard');
    } else {
      console.log('❌ FAILED: Token is invalid or expired');
      console.log('🔧 ACTION: Generate a new token with proper permissions');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

main().catch(console.error);

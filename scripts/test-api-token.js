#!/usr/bin/env node

/**
 * Test Cloudflare API Token directly
 * This script tests if the API token has the correct permissions
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://wikngnwwakatokbgvenw.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbndhYWthdG9rYmdlbnciLCJyb2xlIjoiYW5vbiIsImlhdCI6MTczNDU2NzE5NywiZXhwIjoyMDUwMTQzMTk3fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testApiToken() {
  console.log('🧪 Testing Cloudflare API Token...\n');

  try {
    // Test the edge function to see what error we get
    console.log('📡 Testing edge function response...');
    const response = await fetch(`${SUPABASE_URL}/functions/v1/cloudflare-radar-stats`);
    const data = await response.json();
    
    console.log('📊 Edge Function Response:');
    console.log('  Data Source:', data.dataSource);
    console.log('  Error:', data.error || 'None');
    console.log('  Last Updated:', data.lastUpdated);
    
    if (data.dataSource === 'cloudflare_radar_api') {
      console.log('✅ SUCCESS: API token is working!');
      console.log('  Phishing Total:', data.phishing.total);
      console.log('  DMARC Adoption:', data.dmarc.adoptionRate + '%');
    } else if (data.dataSource === 'fallback_no_token') {
      console.log('❌ ISSUE: No API token found in environment');
      console.log('   → Check that CLOUDFLARE_API_KEY is set in Supabase secrets');
    } else if (data.dataSource === 'fallback_api_error') {
      console.log('❌ ISSUE: API token found but API calls are failing');
      console.log('   → Token may be invalid or lack permissions');
      console.log('   → Check token permissions for Radar API access');
    }

    // Test a simple Cloudflare API endpoint to validate token
    console.log('\n🔍 Testing token permissions...');
    
    // We can't directly access the token from here, but we can suggest manual testing
    console.log('💡 To test your token manually, run:');
    console.log('   curl -H "Authorization: Bearer YOUR_TOKEN" \\');
    console.log('     "https://api.cloudflare.com/client/v4/radar/attacks/layer7/summary"');
    
    console.log('\n📋 Expected token permissions:');
    console.log('   • Zone:Zone:Read');
    console.log('   • Zone:Zone Analytics:Read');
    console.log('   • Account:Cloudflare Radar:Read');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testApiToken();

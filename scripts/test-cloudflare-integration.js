#!/usr/bin/env node

/**
 * Test script to verify Cloudflare Radar integration status
 * This script tests the edge function and reports on data authenticity
 */

// Simple test using fetch - no external dependencies
const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

async function testCloudflareIntegration() {
  console.log('🔍 Testing Cloudflare Radar Integration...\n');

  try {
    console.log('📡 Calling cloudflare-radar-stats edge function...');
    const startTime = Date.now();

    // Call the edge function directly using fetch
    const response = await fetch(`${supabaseUrl}/functions/v1/cloudflare-radar-stats`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    const responseTime = Date.now() - startTime;
    console.log(`⏱️  Response time: ${responseTime}ms`);

    if (!response.ok) {
      console.error('❌ Edge function error:', response.status, response.statusText);
      return;
    }

    if (!data) {
      console.error('❌ No data returned from edge function');
      return;
    }
    
    console.log('\n📊 Integration Status Report:');
    console.log('=' .repeat(50));
    
    // Check data source
    const dataSource = data.dataSource || 'unknown';
    const isRealData = dataSource === 'cloudflare_radar_api';
    
    console.log(`🔗 Data Source: ${dataSource}`);
    console.log(`✅ Real API Data: ${isRealData ? 'YES' : 'NO'}`);
    console.log(`📅 Last Updated: ${data.lastUpdated || 'Unknown'}`);
    
    if (data.error) {
      console.log(`⚠️  Error Message: ${data.error}`);
    }
    
    // Analyze data quality
    console.log('\n📈 Data Quality Analysis:');
    console.log('-'.repeat(30));
    
    if (data.phishing) {
      console.log(`🎯 Phishing Attacks: ${data.phishing.total.toLocaleString()}`);
      console.log(`📊 Phishing Trend: ${data.phishing.trend}%`);
    }
    
    if (data.emailSecurity) {
      console.log(`📧 Malicious Email Rate: ${data.emailSecurity.malicious.detectionRate}%`);
      console.log(`🔐 DKIM Adoption: ${data.emailSecurity.dkim.adoptionRate}%`);
      console.log(`🛡️  SPF Adoption: ${data.emailSecurity.spf.adoptionRate}%`);
    }
    
    if (data.dmarc) {
      console.log(`📮 DMARC Adoption: ${data.dmarc.adoptionRate}%`);
    }
    
    // Check for fallback indicators
    console.log('\n🔍 Fallback Data Detection:');
    console.log('-'.repeat(30));
    
    const fallbackIndicators = [];
    
    // Check for zero values (common in fallback)
    if (data.phishing?.total === 0) fallbackIndicators.push('Zero phishing attacks');
    if (data.emailSecurity?.malicious?.detectionRate === 0) fallbackIndicators.push('Zero malicious email rate');
    
    // Check for exact mock values
    if (data.phishing?.total === 1247892) fallbackIndicators.push('Mock phishing total detected');
    if (data.dmarc?.adoptionRate === 67.2) fallbackIndicators.push('Mock DMARC adoption rate detected');
    
    if (fallbackIndicators.length > 0) {
      console.log('⚠️  Potential fallback data indicators:');
      fallbackIndicators.forEach(indicator => console.log(`   - ${indicator}`));
    } else {
      console.log('✅ No obvious fallback data patterns detected');
    }
    
    // Overall assessment
    console.log('\n🎯 Overall Assessment:');
    console.log('=' .repeat(50));
    
    if (isRealData) {
      console.log('✅ SUCCESS: Integration is working with real Cloudflare Radar API data');
      console.log('🔴 LIVE: Website should display "LIVE THREAT DATA" with pulsing indicators');
    } else {
      console.log('⚠️  WARNING: Integration is using fallback data');
      console.log('🟡 DEMO: Website should display "THREAT PREVIEW" without live indicators');
      
      if (dataSource === 'fallback_no_token') {
        console.log('💡 SOLUTION: Set CLOUDFLARE_API_TOKEN in Supabase secrets');
        console.log('   Command: supabase secrets set CLOUDFLARE_API_TOKEN=your_token_here');
      } else if (dataSource === 'fallback_api_error') {
        console.log('💡 SOLUTION: Check API token validity and Cloudflare Radar API status');
      }
    }
    
    console.log('\n📋 Next Steps:');
    console.log('-'.repeat(20));
    console.log('1. Check the website hero section for correct data source indicators');
    console.log('2. Verify the data updates every 30 minutes');
    console.log('3. Confirm graceful degradation when API is unavailable');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testCloudflareIntegration();

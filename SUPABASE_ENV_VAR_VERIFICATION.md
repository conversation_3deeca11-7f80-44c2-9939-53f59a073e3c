# Supabase Environment Variable Verification Guide

**Issue:** Edge function still showing `fallback_no_token` despite JSR fix
**Required:** `CLOUDFLARE_API_KEY` = `****************************************`

---

## 🔍 **STEP-BY-STEP VERIFICATION**

### **Step 1: Access Supabase Dashboard**
1. Go to: https://supabase.com/dashboard
2. Login to your account
3. Select project: `wikngnwwakatokbgvenw` (BlackVeil project)

### **Step 2: Navigate to Environment Variables**
1. Click **"Settings"** in the left sidebar
2. Click **"Edge Functions"** in the settings menu
3. Look for **"Environment Variables"** or **"Secrets"** section

### **Step 3: Verify Current Configuration**
Check if you see:
- **Name:** `CLOUDFLARE_API_KEY`
- **Value:** `****************************************`

### **Step 4: If Missing or Incorrect**
1. Click **"Add new secret"** or **"New variable"**
2. **Name:** `CLOUDFLARE_API_KEY` (exact spelling)
3. **Value:** `****************************************`
4. Click **"Save"** or **"Add"**

### **Step 5: Force Function Restart**
1. Go to **"Edge Functions"** in the main dashboard
2. Find `cloudflare-radar-stats` function
3. Click **"..."** menu or **"Actions"**
4. Click **"Restart"** or **"Redeploy"**

---

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Variable Not Saving**
- **Cause:** Didn't click "Save" button
- **Solution:** Ensure you click "Save" after entering the variable
- **Verify:** Refresh the page and check if variable is still there

### **Issue 2: Wrong Variable Name**
- **Cause:** Typo in variable name
- **Solution:** Use exact name: `CLOUDFLARE_API_KEY`
- **Note:** Case-sensitive, no spaces

### **Issue 3: Wrong Value**
- **Cause:** Copied token incorrectly
- **Solution:** Use exact value: `****************************************`
- **Note:** No quotes, no extra spaces

### **Issue 4: Supabase UI Differences**
Different Supabase dashboard versions may have:
- **"Secrets"** instead of **"Environment Variables"**
- **"Function Settings"** instead of **"Edge Functions"**
- **"Configuration"** section

---

## ⚡ **ALTERNATIVE: CLI METHOD**

If you have Supabase CLI installed:

```bash
# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref wikngnwwakatokbgvenw

# Set the environment variable
supabase secrets set CLOUDFLARE_API_KEY=****************************************

# Verify it's set
supabase secrets list

# Deploy function to ensure it picks up the new variable
supabase functions deploy cloudflare-radar-stats
```

---

## 🎯 **VERIFICATION TEST**

After configuring the environment variable:

1. **Wait 2-3 minutes** for changes to propagate
2. **Run test:** `node scripts/test-radar-integration.js`
3. **Look for:** `Data Source: cloudflare_radar_api` (instead of `fallback_no_token`)

### **Success Indicators:**
```
📡 Data Source: cloudflare_radar_api
✅ Real API Data: YES
🎯 Phishing Attacks: 5,026,000+
📧 Malicious Email Rate: 3.9%
```

### **Website Changes:**
- ✅ Hero section: **"LIVE THREAT DATA"**
- ✅ Pulsing green indicators active
- ✅ Real threat numbers displayed

---

## 📞 **TROUBLESHOOTING CHECKLIST**

- [ ] Environment variable name is exactly: `CLOUDFLARE_API_KEY`
- [ ] Environment variable value is exactly: `****************************************`
- [ ] Clicked "Save" button in Supabase dashboard
- [ ] Variable appears in the list after saving
- [ ] Waited 2-3 minutes for propagation
- [ ] Restarted edge function if possible
- [ ] Tested with: `node scripts/test-radar-integration.js`

---

## 🏆 **EXPECTED FINAL RESULT**

Once properly configured, your BlackVeil website will:
- ✅ Display **"LIVE THREAT DATA"** in hero section
- ✅ Show real-time threat metrics
- ✅ Have pulsing green indicators
- ✅ Become the **first cybersecurity marketing website** with live threat intelligence

**The integration is 100% ready - just needs the environment variable properly configured!**

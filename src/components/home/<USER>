
import { Link } from "react-router-dom";
import Section from "@/components/Section";
import { <PERSON>, <PERSON><PERSON>he<PERSON>, Clock } from "lucide-react";

interface FeaturesSectionProps {
  background?: "default" | "soft" | "dark" | "grid" | "wave";
}

const FeaturesSection: React.FC<FeaturesSectionProps> = ({ background = "grid" }) => {
  return (
    <Section background={background} className="py-24">
      <div className="text-center mb-16">
        <span className="font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full cyber-glow">
          Our Process
        </span>
        <h2 className="text-3xl md:text-4xl font-bold mt-5 mb-4 cyber-glow-text">How We Protect Your Business</h2>
        <div className="w-24 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6"></div>
        <p className="text-white/80 max-w-2xl mx-auto leading-relaxed">
          Our streamlined three-step approach makes enterprise-grade protection accessible and manageable 
          for businesses of all sizes.
        </p>
      </div>
      
      <div className="grid md:grid-cols-3 gap-10 max-w-6xl mx-auto">
        {/* Feature 1 */}
        <div className="cyber-gradient-card p-8 border border-green-muted/30 rounded-lg transform transition-all duration-500 hover:translate-y-[-8px] hover:shadow-[0_10px_30px_rgba(0,0,0,0.5)] group">
          <div className="mb-6 inline-flex p-4 rounded-lg bg-green-dark/40 group-hover:bg-green-dark/60 transition-all duration-300">
            <Shield className="h-7 w-7 text-green-bright" />
          </div>
          <h3 className="text-xl font-bold mb-4 cyber-glow-text">1. Security Check</h3>
          <p className="text-white/80 mb-5 leading-relaxed">
            We identify all security gaps in your email systems through comprehensive scanning and assessment.
          </p>
          <ul className="cyber-bullet-list text-white/80 mb-6 space-y-2">
            <li>Email vulnerability scanning</li>
            <li>System security testing</li>
            <li>Policy and procedure review</li>
          </ul>
          <Link to="/services" className="inline-flex items-center text-green-bright hover:text-green-light transition-colors font-mono text-sm group-hover:translate-x-1 transform transition-transform duration-300">
            <span>Learn more</span>
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </Link>
        </div>
        
        {/* Feature 2 */}
        <div className="cyber-gradient-card p-8 border border-green-muted/30 rounded-lg transform transition-all duration-500 hover:translate-y-[-8px] hover:shadow-[0_10px_30px_rgba(0,0,0,0.5)] group">
          <div className="mb-6 inline-flex p-4 rounded-lg bg-green-dark/40 group-hover:bg-green-dark/60 transition-all duration-300">
            <FileCheck className="h-7 w-7 text-green-bright" />
          </div>
          <h3 className="text-xl font-bold mb-4 cyber-glow-text">2. Setup</h3>
          <p className="text-white/80 mb-5 leading-relaxed">
            We set up email protection based on our findings, prioritizing critical vulnerabilities first.
          </p>
          <ul className="cyber-bullet-list text-white/80 mb-6 space-y-2">
            <li>Prioritized protection steps</li>
            <li>Clear implementation process</li>
            <li>Cost-effective solutions</li>
          </ul>
          <Link to="/services" className="inline-flex items-center text-green-bright hover:text-green-light transition-colors font-mono text-sm group-hover:translate-x-1 transform transition-transform duration-300">
            <span>Learn more</span>
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </Link>
        </div>
        
        {/* Feature 3 */}
        <div className="cyber-gradient-card p-8 border border-green-muted/30 rounded-lg transform transition-all duration-500 hover:translate-y-[-8px] hover:shadow-[0_10px_30px_rgba(0,0,0,0.5)] group">
          <div className="mb-6 inline-flex p-4 rounded-lg bg-green-dark/40 group-hover:bg-green-dark/60 transition-all duration-300">
            <Clock className="h-7 w-7 text-green-bright" />
          </div>
          <h3 className="text-xl font-bold mb-4 cyber-glow-text">3. Ongoing Protection</h3>
          <p className="text-white/80 mb-5 leading-relaxed">
            We provide continuous monitoring and support to ensure your systems remain secure against evolving threats.
          </p>
          <ul className="cyber-bullet-list text-white/80 mb-6 space-y-2">
            <li>Continuous security monitoring</li>
            <li>Regular security updates</li>
            <li>Threat intelligence reporting</li>
          </ul>
          <Link to="/services" className="inline-flex items-center text-green-bright hover:text-green-light transition-colors font-mono text-sm group-hover:translate-x-1 transform transition-transform duration-300">
            <span>Learn more</span>
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </Link>
        </div>
      </div>
    </Section>
  );
};

export default FeaturesSection;

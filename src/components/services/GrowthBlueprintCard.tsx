
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

const GrowthBlueprintCard = () => {
  return (
    <div id="secure-growth" className="cyber-gradient-card border border-green-muted/30 rounded-lg p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 relative overflow-hidden">
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-green-glow/10 blur-3xl rounded-full"></div>
      
      <div className="flex flex-col md:flex-row gap-6 md:gap-8 lg:gap-10">
        <div className="md:w-2/3">
          <div className="cyber-tag mb-3 xs:mb-4">Premium Service</div>
          <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-bold mb-3 xs:mb-4 cyber-glow-text">Secure Growth Blueprint</h2>
          <p className="text-white/70 mb-4 xs:mb-5 sm:mb-6">
            A 30-day security transformation sprint to develop a comprehensive security framework aligned with your growth objectives.
          </p>
          
          <div className="space-y-2 xs:space-y-3 sm:space-y-4 mb-6 xs:mb-7 sm:mb-8">
            <div className="flex items-start gap-2 xs:gap-3">
              <CheckCircle className="w-4 h-4 xs:w-5 xs:h-5 text-green-bright mt-0.5 flex-shrink-0" />
              <p className="text-white/80 text-sm xs:text-base">Vulnerability assessment and prioritization</p>
            </div>
            <div className="flex items-start gap-2 xs:gap-3">
              <CheckCircle className="w-4 h-4 xs:w-5 xs:h-5 text-green-bright mt-0.5 flex-shrink-0" />
              <p className="text-white/80 text-sm xs:text-base">Growth-aligned security implementation plan</p>
            </div>
            <div className="flex items-start gap-2 xs:gap-3">
              <CheckCircle className="w-4 h-4 xs:w-5 xs:h-5 text-green-bright mt-0.5 flex-shrink-0" />
              <p className="text-white/80 text-sm xs:text-base">Implementation templates for critical areas</p>
            </div>
            <div className="flex items-start gap-2 xs:gap-3">
              <CheckCircle className="w-4 h-4 xs:w-5 xs:h-5 text-green-bright mt-0.5 flex-shrink-0" />
              <p className="text-white/80 text-sm xs:text-base">60-day technical support for implementation</p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Link to="/contact" className="cyber-button inline-flex">
              Request a Consultation
            </Link>
            <Button variant="outline" className="group" asChild>
              <Link to="/contact" className="border-green-muted/50 text-white/80 hover:text-green-bright">
                <span>Learn More</span>
                <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>
        </div>
        
        <div className="md:w-1/3 flex justify-center mt-6 md:mt-0">
          <div className="relative p-3 xs:p-4 sm:p-5 bg-black-soft rounded-lg border border-green-muted/20 shadow-[0_0_25px_rgba(0,255,140,0.1)] w-full max-w-[250px]">
            <FileCheck className="w-10 h-10 xs:w-12 xs:h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mx-auto mb-3 xs:mb-4 text-green-bright opacity-80" />
            <div className="text-center">
              <div className="font-mono text-xs uppercase text-green-bright/80 mb-1">Premium Service</div>
              <Separator className="bg-green-muted/30 my-3 xs:my-4" />
              <ul className="text-white/60 text-xs xs:text-sm space-y-2 text-left">
                <li className="flex items-center">
                  <span className="text-green-bright mr-2">•</span>
                  <span>30-day transformation</span>
                </li>
                <li className="flex items-center">
                  <span className="text-green-bright mr-2">•</span>
                  <span>Expert guidance</span>
                </li>
                <li className="flex items-center">
                  <span className="text-green-bright mr-2">•</span>
                  <span>Ongoing support</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GrowthBlueprintCard;

import React from "react";
import { Helmet } from "react-helmet";

interface OrganizationSchemaProps {
  name?: string;
  url?: string;
  logo?: string;
  sameAs?: string[];
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
}

const OrganizationSchema: React.FC<OrganizationSchemaProps> = ({
  name = "BlackVeil",
  url = "https://blackveil.co.nz",
  logo = "https://blackveil.co.nz/lovable-uploads/497cce26-2e85-4fb5-8c91-983cebc78bc4.png",
  sameAs = [
    // TODO: Add verified social media profiles only
    // Current profiles may not exist - verify before including
  ],
  description = "BlackVeil provides enterprise-grade cybersecurity protection for New Zealand SMBs.",
  contactEmail = "<EMAIL>",
  contactPhone,
  address
}) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name,
    url,
    logo,
    sameAs,
    description,
    contactPoint: contactEmail || contactPhone
      ? [
          {
            "@type": "ContactPoint",
            email: contactEmail,
            telephone: contactPhone,
            contactType: "customer support"
          }
        ]
      : undefined,
    address: address
      ? {
          "@type": "PostalAddress",
          ...address
        }
      : undefined
  };

  // Remove undefined fields
  const cleanSchema = JSON.parse(JSON.stringify(schema));

  return (
    <Helmet>
      <script type="application/ld+json">{JSON.stringify(cleanSchema)}</script>
    </Helmet>
  );
};

export default OrganizationSchema;